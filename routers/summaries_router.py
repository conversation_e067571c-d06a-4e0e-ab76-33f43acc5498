from fastapi import APIRouter, HTTPException
from typing import Optional
from models.data_models import (
    CreateSummaryRequest, SearchRequest, DeleteRequest, ApiResponse
)
from services.storage_service import storage_service

router = APIRouter(prefix="/api/v1/summaries", tags=["Summaries"])

@router.post("/", response_model=ApiResponse)
async def create_summary(request: CreateSummaryRequest):
    """Lưu summary từ server khác (đã có embedding)"""
    try:
        # Tách embedding ra khỏi data
        embedding = request.embedding
        summary_data = request.dict()
        summary_data.pop("embedding")  # Loại bỏ embedding khỏi MongoDB data
        
        summary_id = await storage_service.save_summary(summary_data, embedding)
        
        return ApiResponse(
            success=True,
            message="Summary đã được lưu",
            data={"summary_id": summary_id}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi lưu summary: {str(e)}")

@router.get("/{conversation_id}", response_model=ApiResponse)
async def get_conversation_summaries(conversation_id: str, limit: int = 100):
    """Lấy summaries của conversation"""
    try:
        summaries = await storage_service.get_summaries(
            conversation_id=conversation_id,
            limit=limit
        )
        
        return ApiResponse(
            success=True,
            message=f"Lấy được {len(summaries)} summaries",
            data={"summaries": summaries},
            count=len(summaries)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi lấy summaries: {str(e)}")

@router.post("/search", response_model=ApiResponse)
async def search_summaries(request: SearchRequest):
    """Tìm kiếm summaries bằng vector similarity"""
    try:
        results = await storage_service.search_summaries(
            query_embedding=request.embedding,
            conversation_id=request.conversation_id,
            limit=request.limit
        )
        
        return ApiResponse(
            success=True,
            message=f"Tìm thấy {len(results)} kết quả",
            data={"results": results},
            count=len(results)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi tìm kiếm: {str(e)}")

@router.delete("/", response_model=ApiResponse)
async def delete_summaries(request: DeleteRequest):
    """Xóa summaries"""
    try:
        deleted_count = await storage_service.delete_summaries(
            summary_id=request.summary_id,
            conversation_id=request.conversation_id
        )
        
        return ApiResponse(
            success=True,
            message=f"Đã xóa {deleted_count} summaries",
            data={"deleted_count": deleted_count}
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi xóa summaries: {str(e)}")

@router.get("/", response_model=ApiResponse)
async def get_all_summaries(limit: int = 100):
    """Lấy tất cả summaries"""
    try:
        summaries = await storage_service.get_summaries(limit=limit)
        
        return ApiResponse(
            success=True,
            message=f"Lấy được {len(summaries)} summaries",
            data={"summaries": summaries},
            count=len(summaries)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi lấy summaries: {str(e)}")
