from urllib import response
from fastapi import APIRouter, HTTPException
from typing import Optional
from datetime import datetime
from models.schemas import (
    CreateChatRequest, UpdateTitleRequest, CreateMessageRequest, SearchMessageRequest, DeleteRequest, ApiResponse
)
from services.storage_service import storage_service

router = APIRouter(prefix="/api/chats", tags=["Chats"])



@router.post("/message", response_model=ApiResponse)
async def update_chat(request):
    try:
        # call api lấy ra message từ server khác
        chat = await storage_service.add_message_to_chat(request.conversation_id, request.dict())
        # Lưu message vào chat
       
        return ApiResponse(
            success=True,
            message="Summary đã được lưu",
            data={"summary_id": 1}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi lưu summary: {str(e)}")

@router.post("", response_model=ApiResponse)
async def create_chat(request: CreateChatRequest):
    """Tạo chat mới"""
    try:
        chat_id = await storage_service.create_chat(request.dict())
        
        return ApiResponse(
            success=True,
            message="Chat đã được tạo",
            data={"chat_id": chat_id}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi tạo chat: {str(e)}")

@router.post("/title", response_model=ApiResponse)
async def update_title_chat(request: UpdateTitleRequest):
    """Cập nhật title của chat"""
    try:
        success = await storage_service.update_title_chat(request.conversation_id, request.title)
        
        return ApiResponse(
            success=success,
            message="Title đã được cập nhật" if success else "Không có thay đổi",
            data={"success": success}
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi cập nhật title: {str(e)}")