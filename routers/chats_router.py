from fastapi import APIRouter, HTTPException
from typing import Optional
from datetime import datetime
from models.schemas import (
    CreateChatRequest, CreateMessageRequest, SearchMessageRequest, DeleteRequest, ApiResponse
)
from services.storage_service import storage_service

router = APIRouter(prefix="/api/chats", tags=["Chats"])



@router.post("/message", response_model=ApiResponse)
async def update_chat(request):
    try:
        # call api lấy ra message từ server khác
        
        # Lưu message vào chat
       
        return ApiResponse(
            success=True,
            message="Summary đã được lưu",
            data={"summary_id": 1}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi lưu summary: {str(e)}")

@router.post("", response_model=ApiResponse)
async def create_chat(request: CreateChatRequest):
    """Tạo chat mới"""
    try:
        chat_id = await storage_service.create_chat(request.dict())
        
        return ApiResponse(
            success=True,
            message="Chat đã được tạo",
            data={"chat_id": chat_id}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi tạo chat: {str(e)}")