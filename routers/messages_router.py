from fastapi import APIRouter, HTTPException
from typing import Optional
from datetime import datetime
from models.data_models import (
    CreateMessageRequest, SearchMessageRequest, DeleteRequest, ApiResponse
)
from services.storage_service import storage_service

router = APIRouter(prefix="/api/messages", tags=["Messages"])

@router.post("/", response_model=ApiResponse)
async def create_message(request: CreateMessageRequest):
    """Lưu tin nhắn từ server khác"""
    try:
        message_id = await storage_service.save_message(request.dict())
        
        return ApiResponse(
            success=True,
            message="Tin nhắn đã được lưu",
            data={"message_id": message_id}
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi lưu tin nhắn: {str(e)}")

@router.post("/search", response_model=ApiResponse)
async def search_messages(request: SearchMessageRequest):
    """T<PERSON><PERSON> kiếm tin nhắn theo điề<PERSON> kiện"""
    try:
        messages = await storage_service.get_messages(
            conversation_id=request.conversation_id,
            role=request.role,
            limit=request.limit,
            start_date=request.start_date,
            end_date=request.end_date
        )
        
        return ApiResponse(
            success=True,
            message=f"Tìm thấy {len(messages)} tin nhắn",
            data={"messages": messages},
            count=len(messages)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi tìm kiếm: {str(e)}")

@router.get("/{conversation_id}", response_model=ApiResponse)
async def get_conversation_messages(conversation_id: str, limit: int = 100):
    """Lấy tin nhắn của conversation"""
    try:
        messages = await storage_service.get_messages(
            conversation_id=conversation_id,
            limit=limit
        )
        
        return ApiResponse(
            success=True,
            message=f"Lấy được {len(messages)} tin nhắn",
            data={"messages": messages},
            count=len(messages)
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi lấy tin nhắn: {str(e)}")

@router.delete("/", response_model=ApiResponse)
async def delete_messages(request: DeleteRequest):
    """Xóa tin nhắn"""
    try:
        deleted_count = await storage_service.delete_messages(
            message_id=request.message_id,
            conversation_id=request.conversation_id
        )
        
        return ApiResponse(
            success=True,
            message=f"Đã xóa {deleted_count} tin nhắn",
            data={"deleted_count": deleted_count}
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Lỗi xóa tin nhắn: {str(e)}")
