from fastapi import FastAP<PERSON>
from contextlib import asynccontextmanager
import uvicorn
import sys
import os

from routers.chats_router import router as messages_router
from routers.summaries_router import router as summaries_router
from services.storage_service import storage_service
from config.settings import settings

import logging
import sys

# ===== LOGGING CONFIGURATION =====
def setup_logging():
    """Configure logging for the entire application"""
    # Root logger configuration
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    
    # Clear existing handlers (tránh duplicate)
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Custom formatter
    formatter = logging.Formatter(
        fmt="%(asctime)s | %(name)s | %(levelname)s | [%(filename)s:%(lineno)d] - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.DEBUG)
    
    # Optional: File handler
    # file_handler = logging.FileHandler("app.log")
    # file_handler.setFormatter(formatter)
    # file_handler.setLevel(logging.INFO)
    
    # Add handlers to root logger
    root_logger.addHandler(console_handler)
    # root_logger.addHandler(file_handler)
    
    # Configure specific loggers
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    logging.getLogger("httpx").setLevel(logging.WARNING)

# ===== SETUP LOGGING NGAY KHI IMPORT MODULE =====
setup_logging()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await storage_service.connect()
    print("🚀 Storage service connected successfully!")
    
    yield
    
    # Shutdown
    await storage_service.close()
    print("📴 Storage service connection closed")

# Tạo FastAPI app
app = FastAPI(
    title="Chat Storage Service",
    description="Dịch vụ lưu trữ chat messages và summaries",
    version="1.0.0",
    lifespan=lifespan
)

# Include routers
app.include_router(messages_router)
app.include_router(summaries_router)

# Health check và stats
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "Chat Storage Service",
        "version": "1.0.0"
    }

@app.get("/")
async def root():
    return {
        "message": "Chat Storage API is running!",
        "endpoints": {
            "messages": "/api/messages",
            "summaries": "/api/summaries",
            "docs": "/docs",
            "health": "/health"
        }
    }

@app.get("/api/stats/{conversation_id}")
async def get_conversation_stats(conversation_id: str):
    """Lấy thống kê conversation"""
    try:
        stats = await storage_service.get_conversation_stats(conversation_id)
        return {
            "success": True,
            "message": "Thống kê conversation",
            "data": stats
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"Lỗi lấy thống kê: {str(e)}"
        }

if __name__ == "__main__":
    # Lấy port từ command line hoặc environment
    port = settings.PORT
    
    # Từ command line: python main.py --port 8080
    if "--port" in sys.argv:
        port_index = sys.argv.index("--port") + 1
        if port_index < len(sys.argv):
            port = int(sys.argv[port_index])
    
    # Từ environment variable
    elif os.getenv("PORT"):
        port = int(os.getenv("PORT"))
    
    print(f"🚀 Starting Chat Storage Service on port {port}")
    
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=port,
        reload=True
    )
