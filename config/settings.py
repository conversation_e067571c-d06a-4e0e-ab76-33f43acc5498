import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    # Server
    HOST = os.getenv("HOST", "0.0.0.0")
    PORT = int(os.getenv("PORT", 8000))
    
    # MongoDB
    MONGODB_URL = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
    MONGODB_DATABASE = os.getenv("MONGODB_DATABASE", "chat_storage")
    
    # Qdrant
    QDRANT_HOST = os.getenv("QDRANT_HOST", "localhost")
    QDRANT_PORT = int(os.getenv("QDRANT_PORT", 6333))
    MESSAGES_COLLECTION = os.getenv("MESSAGES_COLLECTION", "chat_messages")
    SUMMARIES_COLLECTION = os.getenv("SUMMARIES_COLLECTION", "chat_summaries")

settings = Settings()
