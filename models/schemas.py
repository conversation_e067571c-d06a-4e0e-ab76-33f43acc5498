from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field



class ChatMessage(BaseModel):
    message_id: str
    role: str
    content: str
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Optional[Dict[str, Any]] = None
    
    
class CreateMessageRequest(BaseModel):
    message_id: str
    role: str
    content: str
    timestamp: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None

# Chat Models
class Chat(BaseModel):
    conversation_id: str
    title: Optional[str] = None
    description: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    metadata: Optional[Dict[str, Any]] = None

class CreateChatRequest(BaseModel):
    user_id: int

class UpdateTitleRequest(BaseModel):
    conversation_id: str = Field(..., description="ID của conversation cần update title")
    title: str = Field(..., description="Title mới cho chat", min_length=1, max_length=200)


# Summary Models
class ChatSummary(BaseModel):
    summary_id: str
    conversation_id: str
    summary_text: str
    message_range: Dict[str, Any]  # {"start_message": "msg-1", "end_message": "msg-10", "count": 10}
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Optional[Dict[str, Any]] = None

class CreateSummaryRequest(BaseModel):
    summary_id: str
    conversation_id: str
    summary_text: str
    message_range: Dict[str, Any]
    embedding: List[float]  # Server khác sẽ gửi embedding sẵn
    timestamp: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None

# Search Models
class SearchRequest(BaseModel):
    query: str
    embedding: List[float]  # Server khác gửi embedding của query
    conversation_id: Optional[str] = None
    limit: int = Field(default=10, ge=1, le=100)

class SearchMessageRequest(BaseModel):
    conversation_id: Optional[str] = None
    role: Optional[str] = None
    limit: int = Field(default=100, ge=1, le=1000)
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

# Delete Models
class DeleteRequest(BaseModel):
    message_id: Optional[str] = None
    conversation_id: Optional[str] = None
    summary_id: Optional[str] = None

# Response Model
class ApiResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    count: Optional[int] = None
