from motor.motor_asyncio import AsyncIOMotor<PERSON>lient
from qdrant_client import Qdrant<PERSON>lient
from qdrant_client.models import Distance, VectorParams, PointStruct, Filter, FieldCondition, MatchValue
from typing import List, Optional, Dict, Any
from config.settings import settings
from datetime import datetime
import uuid

class StorageService:
    def __init__(self):
        self.mongo_client = None
        self.mongo_db = None
        self.qdrant_client = None
        
    async def connect(self):
        """Kết nối databases"""
        # MongoDB
        self.mongo_client = AsyncIOMotorClient(settings.MONGODB_URL)
        self.mongo_db = self.mongo_client[settings.MONGODB_DATABASE]
        
        # Qdrant
        self.qdrant_client = QdrantClient(
            host=settings.QDRANT_HOST,
            port=settings.QDRANT_PORT
        )
        
        # Tạo Qdrant collections
        for collection in [settings.MESSAGES_COLLECTION, settings.SUMMARIES_COLLECTION]:
            try:
                self.qdrant_client.create_collection(
                    collection_name=collection,
                    vectors_config=VectorParams(size=1536, distance=Distance.COSINE)
                )
            except:
                pass  # Collection exists
                
    async def close(self):
        """Đóng connections"""
        if self.mongo_client:
            self.mongo_client.close()

    # MESSAGE OPERATIONS
    async def save_message(self, message_data: Dict[str, Any]) -> str:
        """Lưu tin nhắn từ server khác"""
        if not message_data.get("timestamp"):
            message_data["timestamp"] = datetime.now()
            
        # Lưu vào MongoDB
        collection = self.mongo_db.messages
        await collection.insert_one(message_data)
        
        return message_data["message_id"]
    
    async def get_messages(self, conversation_id: Optional[str] = None, 
                          role: Optional[str] = None, limit: int = 100,
                          start_date: Optional[datetime] = None,
                          end_date: Optional[datetime] = None) -> List[Dict]:
        """Lấy tin nhắn theo điều kiện"""
        collection = self.mongo_db.messages
        
        # Xây dựng query
        query = {}
        if conversation_id:
            query["conversation_id"] = conversation_id
        if role:
            query["role"] = role
        if start_date or end_date:
            query["timestamp"] = {}
            if start_date:
                query["timestamp"]["$gte"] = start_date
            if end_date:
                query["timestamp"]["$lte"] = end_date
                
        cursor = collection.find(query).sort("timestamp", -1).limit(limit)
        
        messages = []
        async for doc in cursor:
            doc.pop("_id", None)
            messages.append(doc)
            
        return messages

    async def delete_messages(self, message_id: Optional[str] = None, 
                            conversation_id: Optional[str] = None) -> int:
        """Xóa tin nhắn"""
        if not message_id and not conversation_id:
            raise ValueError("Cần message_id hoặc conversation_id")
            
        collection = self.mongo_db.messages
        
        query = {}
        if message_id:
            query["message_id"] = message_id
        elif conversation_id:
            query["conversation_id"] = conversation_id
        
        result = await collection.delete_many(query)
        return result.deleted_count

    # SUMMARY OPERATIONS
    async def save_summary(self, summary_data: Dict[str, Any], embedding: List[float]) -> str:
        """Lưu summary từ server khác"""
        if not summary_data.get("timestamp"):
            summary_data["timestamp"] = datetime.now()
            
        # Lưu vào MongoDB
        collection = self.mongo_db.summaries
        await collection.insert_one(summary_data)
        
        # Lưu vào Qdrant với embedding
        point = PointStruct(
            id=str(uuid.uuid4()),
            vector=embedding,
            payload={
                "summary_id": summary_data["summary_id"],
                "conversation_id": summary_data["conversation_id"],
                "summary_text": summary_data["summary_text"],
                "message_range": summary_data["message_range"],
                "timestamp": summary_data["timestamp"].isoformat()
            }
        )
        
        self.qdrant_client.upsert(
            collection_name=settings.SUMMARIES_COLLECTION,
            points=[point]
        )
        
        return summary_data["summary_id"]

    async def get_summaries(self, conversation_id: Optional[str] = None, 
                           limit: int = 100) -> List[Dict]:
        """Lấy summaries"""
        collection = self.mongo_db.summaries
        
        query = {}
        if conversation_id:
            query["conversation_id"] = conversation_id
            
        cursor = collection.find(query).sort("timestamp", -1).limit(limit)
        
        summaries = []
        async for doc in cursor:
            doc.pop("_id", None)
            summaries.append(doc)
            
        return summaries

    async def search_summaries(self, query_embedding: List[float], 
                              conversation_id: Optional[str] = None, 
                              limit: int = 10) -> List[Dict]:
        """Tìm kiếm summaries bằng vector"""
        search_filter = None
        if conversation_id:
            search_filter = Filter(
                must=[FieldCondition(key="conversation_id", match=MatchValue(value=conversation_id))]
            )
        
        results = self.qdrant_client.search(
            collection_name=settings.SUMMARIES_COLLECTION,
            query_vector=query_embedding,
            query_filter=search_filter,
            limit=limit
        )
        
        search_results = []
        for result in results:
            search_results.append({
                "score": result.score,
                "summary_id": result.payload["summary_id"],
                "conversation_id": result.payload["conversation_id"],
                "summary_text": result.payload["summary_text"],
                "message_range": result.payload["message_range"],
                "timestamp": result.payload["timestamp"]
            })
            
        return search_results

    async def delete_summaries(self, summary_id: Optional[str] = None, 
                              conversation_id: Optional[str] = None) -> int:
        """Xóa summaries"""
        if not summary_id and not conversation_id:
            raise ValueError("Cần summary_id hoặc conversation_id")
            
        collection = self.mongo_db.summaries
        
        # Xóa từ MongoDB
        query = {}
        qdrant_filter = None
        
        if summary_id:
            query["summary_id"] = summary_id
            qdrant_filter = Filter(
                must=[FieldCondition(key="summary_id", match=MatchValue(value=summary_id))]
            )
        elif conversation_id:
            query["conversation_id"] = conversation_id
            qdrant_filter = Filter(
                must=[FieldCondition(key="conversation_id", match=MatchValue(value=conversation_id))]
            )
        
        result = await collection.delete_many(query)
        
        # Xóa từ Qdrant
        try:
            if qdrant_filter:
                self.qdrant_client.delete(
                    collection_name=settings.SUMMARIES_COLLECTION,
                    points_selector=qdrant_filter
                )
        except:
            pass
            
        return result.deleted_count

    # UTILITY OPERATIONS
    async def get_conversation_stats(self, conversation_id: str) -> Dict:
        """Lấy thống kê conversation"""
        # Messages stats
        messages_collection = self.mongo_db.messages
        total_messages = await messages_collection.count_documents({"conversation_id": conversation_id})
        user_messages = await messages_collection.count_documents({"conversation_id": conversation_id, "role": "user"})
        assistant_messages = await messages_collection.count_documents({"conversation_id": conversation_id, "role": "assistant"})
        
        # Summaries stats
        summaries_collection = self.mongo_db.summaries
        total_summaries = await summaries_collection.count_documents({"conversation_id": conversation_id})
        
        # Latest message
        latest_message = await messages_collection.find_one(
            {"conversation_id": conversation_id},
            sort=[("timestamp", -1)]
        )
        if latest_message:
            latest_message.pop("_id", None)
        
        return {
            "conversation_id": conversation_id,
            "total_messages": total_messages,
            "user_messages": user_messages,
            "assistant_messages": assistant_messages,
            "total_summaries": total_summaries,
            "latest_message": latest_message
        }

# Global instance
storage_service = StorageService()
